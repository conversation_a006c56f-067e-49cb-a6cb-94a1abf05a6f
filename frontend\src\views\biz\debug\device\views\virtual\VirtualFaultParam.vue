<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :cell-style="cellStyle"
      :columns="columns"
      highlight-current-row
      table-key="virtualFaultParam"
      :request-api="getParamList"
      :init-param="initParam"
      :request-auto="true"
      row-key="name"
      :data-callback="dataCallback"
    >
      <template #operation="scope">
        <el-button type="primary" link :icon="EditPen" @click="confirmSelect(scope.row)">{{ t("device.virtualParam.confirm") }}</el-button>
      </template>
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <div class="flex flex-wrap gap-4 items-center header">
          <el-checkbox v-model="refreshCheck" :label="t('device.virtualParam.autoRefresh')" size="large" />

          <el-button type="primary" plain :icon="Refresh" @click="handleButtonClick('refresh')">{{ t("device.virtualParam.refresh") }}</el-button>
          <el-button type="primary" :icon="CircleCheck" @click="handleConfirm">{{ t("device.virtualParam.confirm") }}</el-button>
          <el-button type="success" :icon="Upload" @click="handleButtonClick('import')">{{ t("device.virtualParam.import") }}</el-button>
          <el-button type="success" :icon="Download" @click="handleButtonClick('export')">{{ t("device.virtualParam.export") }}</el-button>
        </div>
      </template>
    </ProTable>
  </div>
  <ProgressDialog ref="progressDialog"></ProgressDialog>
</template>

<script setup lang="tsx" name="VirtualFaultParam">
import { ref, reactive, onUnmounted, onBeforeUnmount, nextTick, watch } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import { virtualDeviceApi, VirtualFaultParam } from "@/api/modules/biz/debug/virtualDevice";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { EditPen, Refresh, CircleCheck, Upload, Download } from "@element-plus/icons-vue";
import { useDebugStore } from "@/stores/modules/debug";
import { osControlApi } from "@/api/modules/biz/os";
import { useConfigStore } from "@/stores/modules";
import ProgressDialog from "../../dialog/ProgressDialog.vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const { addConsole } = useDebugStore();
const { paramInfo } = useConfigStore();

// 定义 props
const props = defineProps<{ deviceId: string }>();
const progressDialog = ref();
const searchName = ref("");
const searchDescription = ref("");
const refreshCheck = ref(false);

// ProTable 实例
const proTable = ref<ProTableInstance>();

// 初始化请求参数
const initParam = reactive({ cmdType: "read_fault_para" });

const showLoading = () => {
  progressDialog.value.show();
};
const hideLoading = () => {
  progressDialog.value.hide();
};

// 处理返回的表格数据
const dataCallback = async (data: any): Promise<{ list: any[]; total: number }> => {
  hideLoading();
  try {
    if (!data || !Array.isArray(data.list)) {
      console.warn("Invalid data received:", data);
      return { list: [], total: 0 };
    }

    // 直接在原数据上添加 isModified 和 originalValue 属性
    data.list.forEach((item: VirtualFaultParam) => {
      item.isModified = false;
      item.originalDataValue = item.dataValue; // 保存原始幅值
    });

    return {
      list: data.list,
      total: data.total
    };
  } catch (error) {
    console.error("Error in dataCallback:", error);
    throw error;
  } finally {
    hideLoading();
  }
};

// 获取参数列表
const getParamList = async (params: any) => {
  let newParams = JSON.parse(JSON.stringify(params));
  newParams.cmdType = "read_fault_para";
  // newParams.name = searchName.value;
  // newParams.description = searchDescription.value;

  try {
    const result = await virtualDeviceApi.getVirtualParamsByDevice(props.deviceId, "read_fault_para", newParams);
    console.log("Fetched data:", result);
    return result || { list: [], total: 0 };
  } catch (error) {
    console.error("Error fetching data:", error);
    return { list: [], total: 0 };
  }
};

// 确认修改
const confirmSelect = async (row: VirtualFaultParam) => {
  const modifiedData = row.isModified ? [row] : [];

  if (!modifiedData || modifiedData.length === 0) {
    ElMessageBox.alert(t("device.virtualParam.noDataToConfirm"), t("device.virtualParam.warning"), {
      type: "warning",
      confirmButtonText: t("device.virtualParam.confirm")
    });
    return;
  }

  showLoading();
  try {
    const updateItems = modifiedData.map(item => ({
      id: item.id,
      name: item.name,
      dataValue: item.dataValue
    }));

    const response: any = await virtualDeviceApi.updateVirtualParamsByDevice(props.deviceId, "fault_input", updateItems);

    if (Number(response.code) === 0) {
      addConsole(t("device.virtualParam.paramModifiedSuccess"));
      ElMessageBox.alert(t("device.virtualParam.paramModifiedSuccess"), t("device.virtualParam.success"), {
        confirmButtonText: t("device.virtualParam.confirm"),
        type: "success"
      }).then(() => {
        modifiedData.forEach(item => {
          item.isModified = false;
        });
        setTimeout(() => {
          proTable.value?.getTableList();
        }, 2000);
      });
    } else {
      addConsole(t("device.virtualParam.paramModifiedFailed") + response.msg);
      ElMessageBox.alert(t("device.virtualParam.paramModifiedFailed") + response.msg, t("device.virtualParam.error"), {
        confirmButtonText: t("device.virtualParam.confirm"),
        type: "error"
      });
    }
  } catch (error) {
    console.error(t("device.virtualParam.requestFailed"), error);
    ElMessageBox.alert(t("device.virtualParam.requestFailed"), t("device.virtualParam.error"), {
      confirmButtonText: t("device.virtualParam.confirm"),
      type: "error"
    });
  } finally {
    hideLoading();
  }
};

// 表格配置项
const columns = reactive<ColumnProps[]>([
  { type: "selection", fixed: "left", width: 50 },
  {
    prop: "index",
    label: t("device.virtualParam.sequenceNumber")
  },
  {
    prop: "name",
    label: t("device.virtualParam.name"),
    search: {
      el: "input",
      tooltip: t("device.virtualParam.inputName"),
      props: {
        onKeyup: (e: KeyboardEvent) => {
          if (e.key === "Enter") {
            proTable.value?.search();
          }
        }
      }
    }
  },
  {
    prop: "description",
    label: t("device.virtualParam.description"),
    search: {
      el: "input",
      tooltip: t("device.virtualParam.inputDescription"),
      props: {
        onKeyup: (e: KeyboardEvent) => {
          if (e.key === "Enter") {
            proTable.value?.search();
          }
        }
      }
    }
  },
  {
    prop: "dataValue",
    label: t("device.virtualParam.dataValue"),
    render: scope => {
      const handleChange = async (value: string, row: any) => {
        if (refreshCheck.value) {
          addConsole(t("device.virtualParam.autoRefreshEditForbidden"));
          ElMessageBox.alert(t("device.virtualParam.autoRefreshEditForbidden"), t("device.virtualParam.warning"), {
            type: "warning",
            confirmButtonText: t("device.virtualParam.confirm")
          });
          await nextTick(() => {
            row.dataValue = row.originalDataValue;
          });
          return;
        }
        const validation = isValid(value);
        if (!validation.valid) {
          addConsole(validation.message);
          ElMessageBox.alert(validation.message, t("device.virtualParam.error"), {
            type: "error",
            confirmButtonText: t("device.virtualParam.confirm")
          });
          await nextTick(() => {
            row.dataValue = row.originalDataValue;
          });
          return;
        }
        console.log("Value changed:", value);
        row.isModified = true;
        row.dataValue = Number(value);
      };
      return (
        <div>
          <el-input v-model={scope.row.dataValue} onChange={(value: string) => handleChange(value, scope.row)} />
        </div>
      );
    }
  },
  { prop: "operation", label: t("device.virtualParam.operation"), fixed: "right", width: 200 }
]);

// 数值有效性验证 - 故障参数只支持非负整数
const isValid = (value: string) => {
  const trim = value.trim();
  if (trim.length === 0) {
    return { valid: false, message: t("device.virtualParam.validation.emptyValue") };
  }
  
  const num = Number(trim);
  if (isNaN(num) || !isFinite(num)) {
    return { valid: false, message: t("device.virtualParam.validation.invalidNumber") };
  }
  
  if (!Number.isInteger(num)) {
    return { valid: false, message: t("device.virtualParam.validation.mustBeInteger") };
  }
  
  if (num < 0) {
    return { valid: false, message: t("device.virtualParam.validation.mustBeNonNegative") };
  }
  
  return { valid: true, message: "" };
};

// 按钮处理函数
const handleButtonClick = async (action: string) => {
  switch (action) {
    case "refresh":
      if (proTable.value?.tableData.some(row => row.isModified)) {
        ElMessageBox.confirm(t("device.virtualParam.confirmRefresh"), t("device.virtualParam.warning"), {
          confirmButtonText: t("device.virtualParam.confirm"),
          cancelButtonText: t("device.virtualParam.cancel"),
          type: "warning"
        })
          .then(() => {
            proTable.value?.getTableList();
          })
          .catch(() => {
            // 用户取消刷新
          });
      } else {
        proTable.value?.getTableList();
      }
      break;
    case "import":
      handleImport();
      break;
    case "export":
      handleExport();
      break;
    default:
      console.log(action);
  }
};

// 批量确认处理
const handleConfirm = async () => {
  const modifiedData = proTable.value?.tableData.filter(row => row.isModified);

  if (!modifiedData || modifiedData.length === 0) {
    ElMessageBox.alert(t("device.virtualParam.noDataToConfirm"), t("device.virtualParam.warning"), {
      type: "warning",
      confirmButtonText: t("device.virtualParam.confirm")
    });
    return;
  }

  showLoading();
  try {
    const updateItems = modifiedData.map(item => ({
      id: item.id,
      name: item.name,
      dataValue: item.dataValue
    }));

    const response: any = await virtualDeviceApi.updateVirtualParamsByDevice(props.deviceId, "fault_input", updateItems);

    if (Number(response.code) === 0) {
      addConsole(t("device.virtualParam.paramModifiedSuccess"));
      ElMessageBox.alert(t("device.virtualParam.paramModifiedSuccess"), t("device.virtualParam.success"), {
        confirmButtonText: t("device.virtualParam.confirm"),
        type: "success"
      }).then(() => {
        setTimeout(() => {
          proTable.value?.getTableList();
        }, 2000);
      });
    } else {
      addConsole(t("device.virtualParam.paramModifiedFailed") + response.msg);
      ElMessageBox.alert(t("device.virtualParam.paramModifiedFailed") + response.msg, t("device.virtualParam.error"), {
        confirmButtonText: t("device.virtualParam.confirm"),
        type: "error"
      });
    }
  } catch (error) {
    console.error(t("device.virtualParam.requestFailed"), error);
    ElMessageBox.alert(t("device.virtualParam.requestFailed"), t("device.virtualParam.error"), {
      confirmButtonText: t("device.virtualParam.confirm"),
      type: "error"
    });
  } finally {
    hideLoading();
  }
};

// 导出处理
const handleExport = async () => {
  const defaultPath = "虚拟装置_故障量参数.xlsx";
  const selectPath = await osControlApi.openSaveFileDialogByParams({
    title: t("device.virtualParam.exportTitle"),
    defaultPath,
    filterList: [{ name: "xlsx", extensions: ["xlsx"] }]
  });
  if (!selectPath) {
    return;
  }
  console.log("selectPath:", selectPath);
  const path = String(selectPath);
  showLoading();
  try {
    const response = await virtualDeviceApi.exportVirtualParams(props.deviceId, "read_fault_para", path);
    if (response && response.code === 0) {
      ElMessage.success(t("device.virtualParam.exportSuccess"));
      addConsole(t("device.virtualParam.exportSuccess") + path);
    } else {
      ElMessage.error(response?.msg || t("device.virtualParam.exportFailed"));
      addConsole(`${t("device.virtualParam.exportFailed")}: ${response?.msg || "Unknown error"}`);
    }
  } catch (error) {
    console.error("Export error:", error);
    ElMessageBox.alert(t("device.virtualParam.exportFailed"), t("device.virtualParam.error"), {
      confirmButtonText: t("device.virtualParam.confirm"),
      type: "error"
    });
    addConsole(`${t("device.virtualParam.exportFailed")}: ${error}`);
  } finally {
    hideLoading();
  }
};

// 导入处理
const handleImport = async () => {
  const selectPath = await osControlApi.selectFileByParams({
    title: t("device.virtualParam.importTitle"),
    filterList: [{ name: "xlsx", extensions: ["xlsx"] }]
  });

  if (!selectPath.path) {
    return;
  }

  const path = String(selectPath.path);
  showLoading();
  try {
    const response = await virtualDeviceApi.importVirtualParams(props.deviceId, "fault_input", path);
    if (response && response.code === 0) {
      ElMessage.success(t("device.virtualParam.importSuccess"));
      addConsole(t("device.virtualParam.importSuccess"));
      // 导入成功后刷新表格数据
      proTable.value?.getTableList();
    } else {
      ElMessage.error(response?.msg || t("device.virtualParam.importFailed"));
      addConsole(`${t("device.virtualParam.importFailed")}: ${response?.msg || "Unknown error"}`);
    }
  } catch (error) {
    console.error("Import error:", error);
    ElMessageBox.alert(t("device.virtualParam.importFailed"), t("device.virtualParam.error"), {
      confirmButtonText: t("device.virtualParam.confirm"),
      type: "error"
    });
    addConsole(`${t("device.virtualParam.importFailed")}: ${error}`);
  } finally {
    hideLoading();
  }
};

// 单元格样式
const cellStyle = ({ row }: { row: any; column: any; rowIndex: number; columnIndex: number }) => {
  if (row.isModified) {
    return { backgroundColor: "var(--el-color-warning-light-9)" };
  }
  return {};
};

// 自动刷新监听
watch(refreshCheck, newValue => {
  if (newValue) {
    if (proTable.value?.tableData.some(row => row.isModified)) {
      ElMessageBox.confirm(t("device.virtualParam.confirmAutoRefresh"), t("device.virtualParam.warning"), {
        confirmButtonText: t("device.virtualParam.confirm"),
        cancelButtonText: t("device.virtualParam.cancel"),
        type: "warning"
      })
        .then(() => {
          console.log(t("device.virtualParam.continueAutoRefresh"));
          startRefreshTimer();
        })
        .catch(() => {
          refreshCheck.value = false;
        });
    } else {
      startRefreshTimer();
    }
  } else {
    stopRefreshTimer();
  }
});

// 定时器相关逻辑
let refreshTimer: NodeJS.Timeout | null = null;
const startRefreshTimer = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
  }
  refreshTimer = setInterval(() => {
    proTable.value?.getTableList();
  }, 1000); // 默认5秒刷新一次
};
const stopRefreshTimer = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
};

onUnmounted(() => {
  stopRefreshTimer();
});

onBeforeUnmount(() => {
  stopRefreshTimer();
});
</script>

<style lang="css" scoped>
.table-box {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
}
.header {
  margin-bottom: 5px;
}
</style>
